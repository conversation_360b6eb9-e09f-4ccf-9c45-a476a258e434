import{d,h as a,ab as O,ac as m,ad as y}from"./index-CIth6Xtx.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.5 2C8.46243 2 6 4.46243 6 7.5 6 10.5376 8.46243 13 11.5 13 14.5376 13 17 10.5376 17 7.5 17 4.46243 14.5376 2 11.5 2zM18.5776 13.0962L22.252 15.977V17.502H13.75V15.502H18.4046L17.3436 14.6701 18.5776 13.0962zM13.7519 18.4983H22.2542V20.4983H17.5993L18.6603 21.3302 17.4263 22.9041 13.7519 20.0233V18.4983zM12.8762 14C12.0139 15.103 11.5 16.4915 11.5 18 11.5 19.5085 12.0139 20.897 12.8762 22H2V20C2 16.6863 4.68629 14 8 14H12.8762z"}}]},P=d({name:"UserTransmitFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-user-transmit-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>m(b,f.value)}});export{P as default};
