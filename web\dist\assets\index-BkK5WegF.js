import{q as t,aN as q,d as J,ag as Q,h as z,i as X,Y as Z,ah as ee,K as v,k as te,ai as ae,f as h,c as T,o as b,g as o,n,F as P,s as M,u as x,b as N,a as y,t as L,p as W,y as B,_ as se}from"./index-CIth6Xtx.js";import{P as oe}from"./index-DV9_uwcE.js";import{T as ie}from"./index-DxiECXx1.js";import{L as $}from"./date-BKmdRdeE.js";import{d as I}from"./dayjs.min-DEhi9l_D.js";import{g as e,a as re}from"./charts-B_DVb_TQ.js";import{i as ne}from"./install-CHcVn6YI.js";import{u as le,i as de,a as ce,b as O}from"./installCanvasRenderer-akfsAsjg.js";import{b as pe,a as me,i as ue}from"./install-DYqkEeHz.js";import"./more-rY-lm5vv.js";import"./shop-DLL4dNJx.js";import"./service-Nm-RE4Lu.js";import"./user-avatar-kkXLBFQP.js";import"./laptop-CdbclMSR.js";const he=[{title:t("pages.dashboardDetail.topPanel.paneList.totalRequest"),number:"1126",upTrend:"10%"},{title:t("pages.dashboardDetail.topPanel.paneList.suppliers"),number:"13",downTrend:"13%"},{title:t("pages.dashboardDetail.topPanel.paneList.productCategory"),number:"4",upTrend:"10%"},{title:t("pages.dashboardDetail.topPanel.paneList.applicant"),number:90,downTrend:"44%",leftType:"icon-file-paste"},{title:t("pages.dashboardDetail.topPanel.paneList.completionRate"),number:80.5,upTrend:"70%"},{title:t("pages.dashboardDetail.topPanel.paneList.arrivalRate"),number:78,upTrend:"16%"}],ge=[{description:t("pages.dashboardDetail.sslDescription"),index:1,isSetup:!0,name:t("pages.dashboardDetail.ssl"),type:4},{description:t("pages.dashboardDetail.sslDescription"),index:1,isSetup:!0,name:t("pages.dashboardDetail.ssl"),type:4}];function R({dateTime:c=[],placeholderColor:i,borderColor:s}){const a=[],g=[],m=[];for(let r=0;r<40;r++){if(c.length>0){const S=(new Date(c[1]).getTime()-new Date(c[0]).getTime())/40,f=new Date(c[0]).getTime()+S*r;a.push(I(f).format("MM-DD"))}else a.push(I().subtract(40-r,"day").format("MM-DD"));g.push(e().toString()),m.push(e().toString())}return{color:q(),xAxis:{data:a,axisLabel:{color:i},splitLine:{show:!1},axisLine:{lineStyle:{color:s,width:1}}},yAxis:{type:"value",axisLabel:{color:i},nameTextStyle:{padding:[0,0,0,60]},axisTick:{show:!1,axisLine:{show:!1}},axisLine:{show:!1},splitLine:{lineStyle:{color:s}}},tooltip:{trigger:"item"},grid:{top:"5px",left:"25px",right:"5px",bottom:"60px"},legend:{left:"center",bottom:"0",orient:"horizontal",data:[t("pages.dashboardDetail.procurement.goods.massageMachine"),t("pages.dashboardDetail.procurement.goods.coffeeMachine")],itemHeight:8,itemWidth:8,textStyle:{fontSize:12,color:i}},series:[{name:t("pages.dashboardDetail.procurement.goods.massageMachine"),symbolSize:10,data:m.reverse(),type:"scatter"},{name:t("pages.dashboardDetail.procurement.goods.coffeeMachine"),symbolSize:10,data:g.concat(g.reverse()),type:"scatter"}]}}function E({dateTime:c=[],placeholderColor:i,borderColor:s}){let l=[];for(let a=1;a<7;a++)l.push(t(`pages.dashboardDetail.chart.week${a}`));return c.length>0&&(l=re(c,7)),{color:q(),grid:{top:"5%",right:"10px",left:"30px",bottom:"60px"},legend:{left:"center",bottom:"0",orient:"horizontal",data:[t("pages.dashboardDetail.procurement.goods.cup"),t("pages.dashboardDetail.procurement.goods.tea"),t("pages.dashboardDetail.procurement.goods.honey"),t("pages.dashboardDetail.procurement.goods.flour")],textStyle:{fontSize:12,color:i}},xAxis:{type:"category",data:l,boundaryGap:!1,axisLabel:{color:i},axisLine:{lineStyle:{color:s,width:1}}},yAxis:{type:"value",axisLabel:{color:i},splitLine:{lineStyle:{color:s}}},tooltip:{trigger:"item"},series:[{showSymbol:!0,symbol:"circle",symbolSize:8,name:t("pages.dashboardDetail.procurement.goods.cup"),stack:"总量",data:[e(),e(),e(),e(),e(),e(),e()],type:"line",itemStyle:{borderColor:s,borderWidth:1}},{showSymbol:!0,symbol:"circle",symbolSize:8,name:t("pages.dashboardDetail.procurement.goods.tea"),stack:"总量",data:[e(),e(),e(),e(),e(),e(),e()],type:"line",itemStyle:{borderColor:s,borderWidth:1}},{showSymbol:!0,symbol:"circle",symbolSize:8,name:t("pages.dashboardDetail.procurement.goods.honey"),stack:"总量",data:[e(),e(),e(),e(),e(),e(),e()],type:"line",itemStyle:{borderColor:s,borderWidth:1}},{showSymbol:!0,symbol:"circle",symbolSize:8,name:t("pages.dashboardDetail.procurement.goods.flour"),stack:"总量",data:[e(),e(),e(),e(),e(),e(),e()],type:"line",itemStyle:{borderColor:s,borderWidth:1}}]}}const be={class:"dashboard-panel-detail"},ye={class:"dashboard-list-card__number"},fe={class:"dashboard-list-card__text"},_e={class:"dashboard-list-card__text-left"},De={name:"DashboardDetail"},xe=J({...De,setup(c){le([pe,me,ue,de,ne,ce]);const i=Q(),s=z(()=>i.chartColors);let l,a;const g=()=>{l=document.getElementById("lineContainer"),a=O(l),a.setOption(E({...s.value}))};let m,r;const S=()=>{m=document.getElementById("scatterContainer"),r=O(m),r.setOption(R({...s.value}))},f=()=>{a==null||a.resize({width:l.clientWidth,height:l.clientHeight}),r==null||r.resize({width:m.clientWidth,height:m.clientHeight})},k=()=>{S(),g()};X(()=>{k(),Z(()=>{f()})});const{width:F,height:H}=ee();v([F,H],()=>{f()}),te(()=>{V(),Y()});const V=v(()=>i.mode,()=>{k()}),Y=v(()=>i.brandTheme,()=>{ae([a,r])}),j=()=>{r.setOption(R({...s.value}))},G=u=>{const p=z(()=>i.chartColors);a.setOption(E({dateTime:u,...p.value}))};return(u,p)=>{const K=h("t-icon"),_=h("t-card"),w=h("t-col"),A=h("t-row"),C=h("t-date-range-picker"),U=h("t-button");return b(),T("div",be,[o(_,{title:u.t("pages.dashboardDetail.topPanel.title"),class:"dashboard-detail-card",bordered:!1},{default:n(()=>[o(A,{gutter:[16,16]},{default:n(()=>[(b(!0),T(P,null,M(x(he),(d,D)=>(b(),N(w,{key:D,xs:6,xl:3},{default:n(()=>[o(_,{class:"dashboard-list-card",description:d.title},{default:n(()=>[y("div",ye,L(d.number),1),y("div",fe,[y("div",_e,[W(L(u.t("pages.dashboardDetail.topPanel.quarter"))+" ",1),o(ie,{class:"icon",type:d.upTrend?"up":"down",describe:d.upTrend||d.downTrend},null,8,["type","describe"])]),o(K,{name:"chevron-right"})])]),_:2},1032,["description"])]),_:2},1024))),128))]),_:1})]),_:1},8,["title"]),o(A,{gutter:[16,16],class:"row-margin"},{default:n(()=>[o(w,{xs:12,xl:9},{default:n(()=>[o(_,{class:"dashboard-detail-card",title:u.t("pages.dashboardDetail.procurement.title"),bordered:!1},{actions:n(()=>[o(C,{class:"card-date-picker-container","default-value":x($),theme:"primary",mode:"date",style:{width:"248px"},onChange:p[0]||(p[0]=d=>G(d))},null,8,["default-value"])]),default:n(()=>[p[1]||(p[1]=y("div",{id:"lineContainer",style:{width:"100%",height:"416px"}},null,-1))]),_:1,__:[1]},8,["title"])]),_:1}),o(w,{xs:12,xl:3},{default:n(()=>[(b(!0),T(P,null,M(x(ge),(d,D)=>(b(),N(oe,{key:D,product:d,class:B({"row-margin":D!==0,"product-card":!0})},null,8,["product","class"]))),128))]),_:1})]),_:1}),o(_,{class:B(["dashboard-detail-card","row-margin"]),title:u.t("pages.dashboardDetail.satisfaction.title"),bordered:!1},{actions:n(()=>[o(C,{class:"card-date-picker-container","default-value":x($),theme:"primary",mode:"date",style:{display:"inline-block","margin-right":"var(--td-comp-margin-s)",width:"248px"},onChange:j},null,8,["default-value"]),o(U,{class:"card-date-button"},{default:n(()=>[W(L(u.t("pages.dashboardDetail.satisfaction.export")),1)]),_:1})]),default:n(()=>[p[2]||(p[2]=y("div",{id:"scatterContainer",style:{width:"100%",height:"434px"}},null,-1))]),_:1,__:[2]},8,["title"])])}}}),$e=se(xe,[["__scopeId","data-v-d9c4c3a3"]]);export{$e as default};
