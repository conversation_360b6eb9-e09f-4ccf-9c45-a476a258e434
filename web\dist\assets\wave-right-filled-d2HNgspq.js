import{d,h as a,ab as C,ac as O,ad as y}from"./index-CIth6Xtx.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M18.0471 1.37012L18.8705 1.9376C20.0768 2.76906 21.1233 3.81549 21.9548 5.02184L22.5223 5.8452 20.8756 6.98024 20.3081 6.15688C19.6146 5.15081 18.7416 4.27779 17.7355 3.58437L16.9121 3.01688 18.0471 1.37012zM13.7523 4.46212C13.2151 3.92523 12.3441 3.92651 11.8086 4.46497L6.55473 9.74721 6.99913 8.65149C7.55869 7.27181 5.99352 6.00154 4.75852 6.83306 4.57016 6.95988 4.41285 7.12759 4.29834 7.32369L1.91873 11.3985C.271144 14.2198.733135 17.7974 3.04341 20.1077L3.88772 20.952C6.69539 23.7597 11.2475 23.7597 14.0552 20.952L19.0896 15.9164C19.6263 15.3796 19.6265 14.5094 19.09 13.9724 18.5531 13.4349 17.6821 13.4347 17.1449 13.9719L15.5413 15.5755 15.0103 15.0444 18.7004 11.3543C19.2372 10.8174 19.2372 9.94702 18.7004 9.41014 18.1635 8.87324 17.2931 8.87323 16.7562 9.41011L13.0661 13.1002 12.5357 12.5699 17.5926 7.51303C18.1294 6.97621 18.1296 6.1059 17.593 5.56887 17.0561 5.03154 16.1852 5.03137 15.6481 5.56849L10.5912 10.6253 10.061 10.0952 13.7525 6.40366C14.2887 5.86749 14.2886 4.99816 13.7523 4.46212z"}}]},m=d({name:"WaveRightFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=C(r),p=a(()=>["t-icon","t-icon-wave-right-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>O(g,v.value)}});export{m as default};
