import{R as a}from"./index-RMfU9fS_.js";import{d as n,f as r,b as p,o as u,n as o,g as l,p as i,t as d}from"./index-CIth6Xtx.js";const _={name:"Result403"},b=n({..._,setup(f){return(t,e)=>{const s=r("t-button");return u(),p(a,{title:"403 Forbidden",tip:t.t("pages.result.403.tips")},{default:o(()=>[l(s,{onClick:e[0]||(e[0]=()=>t.$router.push("/"))},{default:o(()=>[i(d(t.t("pages.result.403.back")),1)]),_:1})]),_:1},8,["tip"])}}});export{b as default};
