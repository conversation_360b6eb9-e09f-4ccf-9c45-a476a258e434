import{d as v,h as a,ab as O,ac as m,ad as y}from"./index-CIth6Xtx.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM7.61869 7.13816L11 9.04017L11 10.5L7.80001 12.9L6.60001 11.3L8.45917 9.90563L6.63816 8.88131L7.61869 7.13816ZM17.3618 8.88131L15.5408 9.90563L17.4 11.3L16.2 12.9L13 10.5L13 9.04017L16.3813 7.13816L17.3618 8.88131ZM9.5 15.5C8.9477 15.5 8.5 15.9477 8.5 16.5V17.5H6.5V16.5C6.5 14.8431 7.84313 13.5 9.5 13.5C10.2994 13.5 11.0276 13.814 11.5645 14.3233C11.6509 14.4053 11.7207 14.4548 11.7711 14.4813C11.7952 14.4939 11.8093 14.4985 11.815 14.5H12.185C12.1907 14.4985 12.2049 14.4939 12.2289 14.4813C12.2793 14.4548 12.3491 14.4053 12.4355 14.3233C12.9724 13.814 13.7006 13.5 14.5 13.5C16.1569 13.5 17.5 14.8431 17.5 16.5V17.5H15.5V16.5C15.5 15.9477 15.0523 15.5 14.5 15.5C14.2329 15.5 13.9921 15.6034 13.812 15.7743C13.4897 16.08 12.9336 16.5 12.1908 16.5H11.8092C11.0664 16.5 10.5103 16.08 10.188 15.7743C10.0079 15.6034 9.76708 15.5 9.5 15.5ZM12.1821 14.5006C12.1821 14.5006 12.1825 14.5005 12.1832 14.5004L12.1821 14.5006ZM11.8179 14.5006C11.8179 14.5006 11.8176 14.5006 11.8168 14.5004L11.8179 14.5006Z"}}]},d=v({name:"Uncomfortable2Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=O(t),C=a(()=>["t-icon","t-icon-uncomfortable-2",o.value]),p=a(()=>c(c({},s.value),r.style)),u=a(()=>({class:C.value,style:p.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>m(L,u.value)}});export{d as default};
