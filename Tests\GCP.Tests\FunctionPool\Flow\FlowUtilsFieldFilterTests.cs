using FluentAssertions;
using GCP.FunctionPool.Flow;
using GCP.DataAccess;
using GCP.Tests.Infrastructure;
using GCP.Common;
using Xunit;
using Xunit.Abstractions;

namespace GCP.Tests.FunctionPool.Flow
{
    public class FlowUtilsFieldFilterTests : DatabaseTestBase
    {
        private readonly ITestOutputHelper _output;
        private readonly FunctionContext _functionContext;

        public FlowUtilsFieldFilterTests(ITestOutputHelper output) : base(output)
        {
            _output = output;
            _functionContext = new FunctionContext
            {
                globalData = new Dictionary<string, object>()
            };

            // 设置数据库上下文
            var dbContext = GetService<IDbContext>();
            _functionContext.LocalDbContext.Value = dbContext;
        }

        [Fact]
        public void BindResult_WithFieldFilter_ShouldRemoveUnwantedFields()
        {
            // Arrange
            // 创建测试数据 - 包含多个字段的字典
            var testData = new Dictionary<string, object>
            {
                { "id", 1 },
                { "name", "测试名称" },
                { "code", "TEST001" },
                { "description", "测试描述" },
                { "status", "active" }
            };

            // 创建FlowData列表 - 只定义了部分字段
            var flowDataList = new List<FlowData>
            {
                new FlowData
                {
                    Key = "id",
                    Type = "int",
                    Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.id" }
                },
                new FlowData
                {
                    Key = "name",
                    Type = "string",
                    Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.name" }
                }
                // 注意：没有定义code、description和status字段
            };

            var engine = FlowUtils.GetEngine(_functionContext);
            var globalData = new Dictionary<string, object>();
            var localVariable = new Dictionary<string, object>
            {
                { "result", testData }
            };

            // Act
            var result = FlowUtils.BindResult(flowDataList, globalData, engine, _functionContext, testData, localVariable);

            // Assert
            result.Should().NotBeNull("结果不应为空");
            result.Should().BeOfType<Dictionary<string, object>>("结果应该是字典类型");

            var resultDict = result as Dictionary<string, object>;
            resultDict.Should().ContainKey("id").And.ContainKey("name");
            resultDict.Should().NotContainKey("code").And.NotContainKey("description").And.NotContainKey("status");
            // 只有在flowDataList中定义的字段应该被保留

            _output.WriteLine($"原始字段数: {testData.Count}, 过滤后字段数: {resultDict.Count}");
            _output.WriteLine($"保留的字段: {string.Join(", ", resultDict.Keys)}");
        }

        [Fact]
        public void BindResult_WithNestedFieldFilter_ShouldRemoveUnwantedFields()
        {
            // Arrange
            // 创建嵌套测试数据
            var nestedData = new Dictionary<string, object>
            {
                { "id", 1 },
                { "user", new Dictionary<string, object>
                    {
                        { "userId", 101 },
                        { "userName", "测试用户" },
                        { "email", "<EMAIL>" },
                        { "phone", "13800138000" }
                    }
                },
                { "extra", "额外信息" }
            };

            // 创建FlowData列表 - 只定义了部分嵌套字段
            var flowDataList = new List<FlowData>
            {
                new FlowData
                {
                    Key = "id",
                    Type = "int",
                    Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.id" }
                },
                new FlowData
                {
                    Key = "user",
                    Type = "object",
                    Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.user" },
                    IsCustomize = true, // 必须设置IsCustomize为true，否则不会进入自定义字段处理逻辑
                    Children = new List<FlowData>
                    {
                        new FlowData
                        {
                            Key = "userId",
                            Type = "int",
                            Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.user.userId" }
                        },
                        new FlowData
                        {
                            Key = "userName",
                            Type = "string",
                            Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.user.userName" }
                        }
                        // 注意：没有定义email和phone字段
                    }
                }
                // 注意：没有定义extra字段
            };

            var engine = FlowUtils.GetEngine(_functionContext);
            var globalData = new Dictionary<string, object>();
            var localVariable = new Dictionary<string, object>
            {
                { "result", nestedData }
            };

            // Act
            var result = FlowUtils.BindResult(flowDataList, globalData, engine, _functionContext, nestedData, localVariable);

            // Assert
            result.Should().NotBeNull("结果不应为空");
            result.Should().BeOfType<Dictionary<string, object>>("结果应该是字典类型");

            var resultDict = result as Dictionary<string, object>;
            resultDict.Should().ContainKey("id").And.ContainKey("user");
            resultDict.Should().NotContainKey("extra");
            // 只有在flowDataList中定义的顶级字段应该被保留

            var userDict = resultDict["user"] as Dictionary<string, object>;
            userDict.Should().NotBeNull("user字段应该是字典类型");
            userDict.Should().ContainKey("userId").And.ContainKey("userName");
            userDict.Should().NotContainKey("email").And.NotContainKey("phone");
            // 只有在flowDataList中定义的嵌套字段应该被保留

            _output.WriteLine($"原始顶级字段数: {nestedData.Count}, 过滤后顶级字段数: {resultDict.Count}");
            _output.WriteLine($"原始user字段数: {(nestedData["user"] as Dictionary<string, object>).Count}, 过滤后user字段数: {userDict.Count}");
            _output.WriteLine($"过滤后user字段: {string.Join(", ", userDict.Keys)}");
        }

        [Fact]
        public void BindResult_WithRootNodeAndFieldFilter_ShouldReturnFilteredRootContent()
        {
            // Arrange
            // 创建真实的测试数据 - 模拟API响应
            var testResult = new Dictionary<string, object>
            {
                { "code", 200 },
                { "data", new Dictionary<string, object>
                    {
                        { "balanceQty", 1 },
                        { "barcodeMitemCode", null },
                        { "barcodeMitemId", null },
                        { "barcodeMitemName", null },
                        { "barcodeUom", null },
                        { "barcodeUomName", null },
                        { "completedQty", 0 },
                        { "creator", "administrator" },
                        { "scanMessage", "" },
                        { "scanSuccess", true },
                        { "workStationCode", null },
                        { "mitemCode", "1010000151" },
                        { "mitemName", "减震器总成/G12SQR/宝马7系（2016-至今）四驱" },
                        { "serialNumber", "SOPSGRA0002Z" },
                        { "extraField1", "应该被过滤的字段1" },
                        { "extraField2", "应该被过滤的字段2" }
                    }
                },
                { "message", "" }
            };

            // 创建ROOT节点配置 - 只定义部分字段
            var flowDataList = new List<FlowData>
            {
                new FlowData
                {
                    Id = "ROOT",
                    Key = "ROOT",
                    Type = "object",
                    Value = new DataValue { Type = "text", TextValue = "" },
                    Description = "根节点",
                    Required = false,
                    IsCustomize = true,
                    Children = new List<FlowData>
                    {
                        new FlowData
                        {
                            Id = "YGZusuNJtZQVH",
                            Key = "code",
                            Type = "int",
                            Value = null,
                            Children = null
                        },
                        new FlowData
                        {
                            Id = "wzzfVgaBeupZq",
                            Key = "data",
                            Type = "object",
                            Value = null,
                            IsCustomize = true,
                            Children = new List<FlowData>
                            {
                                new FlowData
                                {
                                    Id = "oGbzLZoYCcgHC",
                                    Key = "scanMessage",
                                    Type = "string",
                                    Value = null,
                                    Children = null
                                },
                                new FlowData
                                {
                                    Id = "nscdEYnZqUSao",
                                    Key = "scanSuccess",
                                    Type = "bool",
                                    Value = null,
                                    Children = null
                                },
                                new FlowData
                                {
                                    Id = "qbpnDJuFDeMNn",
                                    Type = "string",
                                    Key = "workStationCode",
                                    IsCustomize = true,
                                    Required = false
                                }
                                // 注意：没有定义其他data字段如balanceQty、creator等
                            }
                        },
                        new FlowData
                        {
                            Id = "AIddcvwovgOzr",
                            Key = "message",
                            Type = "string",
                            Value = null,
                            Children = null
                        }
                        // 注意：没有定义其他顶级字段
                    }
                }
            };

            var engine = FlowUtils.GetEngine(_functionContext);
            var globalData = new Dictionary<string, object>();
            var localVariable = new Dictionary<string, object>
            {
                { "result", testResult }
            };

            // Act
            var result = FlowUtils.BindResult(flowDataList, globalData, engine, _functionContext, testResult, localVariable);

            // Assert
            result.Should().NotBeNull("结果不应为空");

            // 验证ROOT节点被正确展开，不应该包含ROOT层级
            result.Should().BeOfType<Dictionary<string, object>>("结果应该是字典类型");
            var resultDict = result as Dictionary<string, object>;

            // 验证只包含定义的顶级字段
            resultDict.Should().ContainKey("code").And.ContainKey("data").And.ContainKey("message");
            resultDict.Should().NotContainKey("ROOT", "不应该包含ROOT层级");

            // 验证data字段被正确过滤
            var dataDict = resultDict["data"] as Dictionary<string, object>;
            dataDict.Should().NotBeNull("data字段应该是字典类型");
            dataDict.Should().ContainKey("scanMessage").And.ContainKey("scanSuccess").And.ContainKey("workStationCode");

            // 验证未定义的字段被过滤掉
            dataDict.Should().NotContainKey("balanceQty", "未定义的字段应该被过滤");
            dataDict.Should().NotContainKey("creator", "未定义的字段应该被过滤");
            dataDict.Should().NotContainKey("extraField1", "额外字段应该被过滤");
            dataDict.Should().NotContainKey("extraField2", "额外字段应该被过滤");

            // 输出调试信息
            var originalDataDict = testResult["data"] as Dictionary<string, object>;
            _output.WriteLine($"原始data字段数: {originalDataDict.Count}, 过滤后data字段数: {dataDict.Count}");
            _output.WriteLine($"保留的data字段: {string.Join(", ", dataDict.Keys)}");
            _output.WriteLine($"过滤掉的data字段: {string.Join(", ", originalDataDict.Keys.Except(dataDict.Keys))}");
            _output.WriteLine($"结果类型: {result.GetType().Name}");
            _output.WriteLine($"是否包含ROOT层级: {(result as Dictionary<string, object>)?.ContainsKey("ROOT")}");
        }
    }
}
