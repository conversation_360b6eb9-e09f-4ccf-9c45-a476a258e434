import{d as f,h as a,ab as d,ac as O,ad as y}from"./index-CIth6Xtx.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.5 2C8.46243 2 6 4.46243 6 7.5 6 10.5376 8.46243 13 11.5 13 14.5376 13 17 10.5376 17 7.5 17 4.46243 14.5376 2 11.5 2zM23.5902 18C23.5902 18 21.9185 22.5 17.5002 22.5 16.5284 22.5 15.6895 22.2823 14.972 21.9427L14.0005 22.9142 12.5863 21.4999 13.3038 20.7824C11.9716 19.5115 11.4102 18 11.4102 18 11.4102 18 13.0838 13.5 17.5002 13.5 18.472 13.5 19.311 13.7179 20.0287 14.0578L21.0006 13.0859 22.4148 14.5002 21.6965 15.2184C23.0283 16.4892 23.5902 18 23.5902 18zM20.287 16.6279L16.5339 20.3809C16.8319 20.4565 17.1533 20.5 17.4985 20.5 20.114 20.5 21.3689 18 21.3689 18 21.3689 18 21.0144 17.2908 20.287 16.6279zM18.4668 15.6197C18.1678 15.5437 17.8451 15.5 17.4985 15.5 14.8778 15.5 13.6289 18 13.6289 18 13.6289 18 13.9849 18.7098 14.7133 19.373L18.4668 15.6197z"}},{tag:"path",attrs:{fill:"currentColor",d:"M9.53584 18.6981L9.27637 17.9996L9.53611 17.3012L9.53676 17.2995L9.53818 17.2957L9.54153 17.2868L9.55026 17.2641C9.55698 17.2467 9.56553 17.225 9.57595 17.1993C9.59677 17.1478 9.62512 17.08 9.66133 16.9981C9.73359 16.8346 9.83804 16.6131 9.97744 16.3527C10.2539 15.8364 10.6805 15.1439 11.2845 14.4436C11.4114 14.2966 11.5472 14.1481 11.6921 14H8C4.68629 14 2 16.6863 2 20V22H11.6913C11.5463 21.8519 11.4105 21.7033 11.2836 21.5562C10.6796 20.8558 10.2533 20.1632 9.97688 19.6467C9.83755 19.3864 9.73318 19.1648 9.66097 19.0013C9.62478 18.9194 9.59645 18.8515 9.57565 18.8C9.56524 18.7743 9.5567 18.7526 9.54998 18.7352L9.54126 18.7125L9.53791 18.7036L9.53649 18.6998L9.53584 18.6981Z"}}]},g=f({name:"UserInvisibleFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-user-invisible-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:C=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:C})}}));return()=>O(b,v.value)}});export{g as default};
