const a=Object.freeze(JSON.parse(`{"displayName":"Log file","fileTypes":["log"],"name":"log","patterns":[{"match":"\\\\b([Tt]race|TRACE)\\\\b:?","name":"comment log.verbose"},{"match":"(?i)\\\\[(v(?:erbose|erb|rb|b?))]","name":"comment log.verbose"},{"match":"(?<=^[p\\\\s\\\\d]*)\\\\bV\\\\b","name":"comment log.verbose"},{"match":"\\\\b(D(?:EBUG|ebug))\\\\b|(?i)\\\\b(debug):","name":"markup.changed log.debug"},{"match":"(?i)\\\\[(d(?:ebug|bug|bg|e?))]","name":"markup.changed log.debug"},{"match":"(?<=^[p\\\\s\\\\d]*)\\\\bD\\\\b","name":"markup.changed log.debug"},{"match":"\\\\b(HINT|INFO|INFORMATION|Info|NOTICE|II)\\\\b|(?i)\\\\b(info(?:|rmation)):","name":"markup.inserted log.info"},{"match":"(?i)\\\\[(i(?:nformation|nfo?|n?))]","name":"markup.inserted log.info"},{"match":"(?<=^[p\\\\s\\\\d]*)\\\\bI\\\\b","name":"markup.inserted log.info"},{"match":"\\\\b(W(?:ARNING|ARN|arn|W))\\\\b|(?i)\\\\b(warning):","name":"markup.deleted log.warning"},{"match":"(?i)\\\\[(w(?:arning|arn|rn|n?))]","name":"markup.deleted log.warning"},{"match":"(?<=^[p\\\\s\\\\d]*)\\\\bW\\\\b","name":"markup.deleted log.warning"},{"match":"\\\\b(ALERT|CRITICAL|EMERGENCY|ERROR|FAILURE|FAIL|Fatal|FATAL|Error|EE)\\\\b|(?i)\\\\b(error):","name":"string.regexp, strong log.error"},{"match":"(?i)\\\\[(error|eror|err?|e|fatal|fatl|ftl|fa?)]","name":"string.regexp, strong log.error"},{"match":"(?<=^[p\\\\s\\\\d]*)\\\\bE\\\\b","name":"string.regexp, strong log.error"},{"match":"\\\\b\\\\d{4}-\\\\d{2}-\\\\d{2}(?=T|\\\\b)","name":"comment log.date"},{"match":"(?<=(^|\\\\s))\\\\d{2}[^\\\\w\\\\s]\\\\d{2}[^\\\\w\\\\s]\\\\d{4}\\\\b","name":"comment log.date"},{"match":"T?\\\\d{1,2}:\\\\d{2}(:\\\\d{2}([,.]\\\\d+)?)?(Z| ?[-+]\\\\d{1,2}:\\\\d{2})?\\\\b","name":"comment log.date"},{"match":"T\\\\d{2}\\\\d{2}(\\\\d{2}([,.]\\\\d+)?)?(Z| ?[-+]\\\\d{1,2}\\\\d{2})?\\\\b","name":"comment log.date"},{"match":"\\\\b(\\\\h{40}|\\\\h{10}|\\\\h{7})\\\\b","name":"constant.language"},{"match":"\\\\b\\\\h{8}-?(\\\\h{4}-?){3}\\\\h{12}\\\\b","name":"constant.language log.constant"},{"match":"\\\\b(\\\\h{2,}[-:])+\\\\h{2,}+\\\\b","name":"constant.language log.constant"},{"match":"\\\\b([0-9]+|true|false|null)\\\\b","name":"constant.language log.constant"},{"match":"\\\\b(0x\\\\h+)\\\\b","name":"constant.language log.constant"},{"match":"\\"[^\\"]*\\"","name":"string log.string"},{"match":"(?<!\\\\w)'[^']*'","name":"string log.string"},{"match":"\\\\b([.A-Za-z]*Exception)\\\\b","name":"string.regexp, emphasis log.exceptiontype"},{"begin":"^[\\\\t ]*at[\\\\t ]","end":"$","name":"string.key, emphasis log.exception"},{"match":"\\\\b[a-z]+://\\\\S+\\\\b/?","name":"constant.language log.constant"},{"match":"(?<![/\\\\\\\\\\\\w])([-\\\\w]+\\\\.)+([-\\\\w])+(?![/\\\\\\\\\\\\w])","name":"constant.language log.constant"}],"scopeName":"text.log"}`)),e=[a];export{e as default};
