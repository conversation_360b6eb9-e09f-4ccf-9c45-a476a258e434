using System.Collections;
using System.Text.Json;
using GCP.Functions.Common.ScriptExtensions;
using Microsoft.AspNetCore.Http;

namespace GCP.Common
{
    /// <summary>
    /// ROOT节点处理工具类
    /// 提供统一的ROOT节点类型转换和数据处理方法
    /// </summary>
    public static class RootNodeHelper
    {
        /// <summary>
        /// ROOT节点的固定键名
        /// </summary>
        public const string RootNodeKey = "ROOT";

        /// <summary>
        /// 根据ROOT节点类型转换数据
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <param name="rootNodeType">ROOT节点类型（object/array）</param>
        /// <returns>转换后的数据</returns>
        private static object ConvertDataByRootNodeType(object data, string rootNodeType)
        {
            if (data == null) return null;

            return rootNodeType?.ToLower() switch
            {
                "array" => ConvertToArray(data),
                "object" => ConvertToObject(data),
                _ => data
            };
        }

        /// <summary>
        /// 将数据转换为数组格式
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>数组格式的数据</returns>
        private static object ConvertToArray(object data)
        {
            if (data == null) return new ArrayList();

            if (data is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Array)
                {
                    var list = new ArrayList();
                    foreach (var item in jsonElement.EnumerateArray())
                    {
                        list.Add(new JavascriptUtils().JSON_PARSE(item.GetRawText()));
                    }
                    return list;
                }
                else
                {
                    // 如果不是数组，包装成数组
                    var list = new ArrayList { new JavascriptUtils().JSON_PARSE(jsonElement.GetRawText()) };
                    return list;
                }
            }
            else if (data is ICollection collection)
            {
                return new ArrayList(collection);
            }
            else
            {
                // 如果不是集合类型，包装成数组
                var list = new ArrayList { data };
                return list;
            }
        }

        /// <summary>
        /// 将数据转换为对象格式
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>对象格式的数据</returns>
        private static object ConvertToObject(object data)
        {
            if (data == null) return new Dictionary<string, object>();

            if (data is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Object)
                {
                    return new JavascriptUtils().JSON_PARSE(jsonElement.GetRawText());
                }
                else if (jsonElement.ValueKind == JsonValueKind.Array)
                {
                    // 如果是数组但ROOT节点要求对象，取第一个元素或包装
                    var firstElement = jsonElement.EnumerateArray().FirstOrDefault();
                    if (firstElement.ValueKind == JsonValueKind.Object)
                    {
                        return new JavascriptUtils().JSON_PARSE(firstElement.GetRawText());
                    }
                }

                return new Dictionary<string, object> { { "data", new JavascriptUtils().JSON_PARSE(jsonElement.GetRawText()) } };
            }
            else if (data is IDictionary<string, object> dictionary)
            {
                return dictionary;
            }

            // 如果是基本类型，包装成对象
            return new Dictionary<string, object> { { "data", data } };
        }

        /// <summary>
        /// 检查FlowData列表中是否有ROOT节点
        /// </summary>
        /// <param name="flowDataList">FlowData列表</param>
        /// <returns>ROOT节点，如果不存在则返回null</returns>
        private static FlowData GetRootNode(List<FlowData> flowDataList)
        {
            return flowDataList.Count == 1 ? flowDataList.FirstOrDefault(x => x.Key == RootNodeKey) : null;
        }

        /// <summary>
        /// 通用的参数ROOT节点处理方法
        /// </summary>
        /// <param name="flowDataList">FlowData配置列表</param>
        /// <param name="resultDic">结果数据字典</param>
        /// <param name="originalResult">原始结果</param>
        /// <returns>处理后的结果</returns>
        public static object ProcessResult(List<FlowData> flowDataList, Dictionary<string, object> resultDic, object originalResult)
        {
            // 检查是否为根节点绑定（只有一个节点且key为ROOT）
            var rootNode = GetRootNode(flowDataList);
            if (rootNode != null)
            {
                // 根节点绑定：如果stepResultDic中有ROOT键，则展开ROOT内容
                if (resultDic.TryGetValue(RootNodeKey, out var rootValue))
                {
                    return ConvertDataByRootNodeType(rootValue, rootNode.Type);
                }
                else
                {
                    return originalResult;
                }
            }

            return resultDic;
        }

        /// <summary>
        /// 为系统API处理请求体数据
        /// </summary>
        /// <param name="requestStream">请求流</param>
        /// <param name="bodyFlowData">请求体FlowData配置</param>
        /// <returns>解析后的数据和ROOT节点信息</returns>
        public static async Task<(object data, FlowData rootNode)> ProcessSystemApiRequestBody(Stream requestStream, List<FlowData> bodyFlowData)
        {
            var rootNode = GetRootNode(bodyFlowData);
            if (rootNode == null)
            {
                // 没有ROOT节点，使用原有逻辑
                var body = await JsonHelper.DeserializeAsync<Dictionary<string, object>>(requestStream);
                return (body, null);
            }

            if (rootNode.Type == "array")
            {
                // ROOT节点是数组类型，直接解析为数组
                var arrayBody = await JsonHelper.DeserializeAsync<object[]>(requestStream);
                return (arrayBody, rootNode);
            }
            else
            {
                // ROOT节点是对象类型，解析为字典
                var objectBody = await JsonHelper.DeserializeAsync<Dictionary<string, object>>(requestStream);
                return (objectBody, rootNode);
            }
        }

        /// <summary>
        /// 为系统API处理查询参数数据
        /// </summary>
        /// <param name="queryCollection">查询参数集合</param>
        /// <param name="paramsFlowData">查询参数FlowData配置</param>
        /// <returns>处理后的数据和ROOT节点信息</returns>
        public static (object data, FlowData rootNode) ProcessSystemApiQueryParams(IQueryCollection queryCollection, List<FlowData> paramsFlowData)
        {
            var rootNode = GetRootNode(paramsFlowData);
            if (rootNode == null)
            {
                // 没有ROOT节点，返回null表示使用原有逻辑
                return (null, null);
            }

            if (rootNode.Type == "array")
            {
                // ROOT节点是数组类型，将所有查询参数组合成数组
                var paramArray = new List<object>();
                foreach (var param in queryCollection)
                {
                    paramArray.Add(new { key = param.Key, value = param.Value.ToString() });
                }
                return (paramArray.ToArray(), rootNode);
            }
            else
            {
                // ROOT节点是对象类型，将查询参数组合成字典
                var paramDict = new Dictionary<string, object>();
                foreach (var param in queryCollection)
                {
                    paramDict[param.Key] = param.Value.ToString();
                }
                return (paramDict, rootNode);
            }
        }

        /// <summary>
        /// 为脚本引擎准备兼容的_data变量
        /// 确保脚本中的_data.xxx.xxx访问方式仍然有效
        /// </summary>
        /// <param name="globalData">全局数据字典</param>
        /// <returns>兼容的数据字典</returns>
        public static Dictionary<string, object> PrepareScriptCompatibleData(Dictionary<string, object> globalData)
        {
            if (globalData == null) return new Dictionary<string, object>();

            var compatibleData = new Dictionary<string, object>();

            foreach (var kvp in globalData)
            {
                if (kvp.Key == RootNodeKey)
                {
                    // 如果是ROOT节点，需要特殊处理以保持脚本兼容性
                    if (kvp.Value is IDictionary<string, object> rootDict)
                    {
                        // ROOT节点是对象类型，将其内容展开到根级别
                        foreach (var rootKvp in rootDict)
                        {
                            compatibleData[rootKvp.Key] = rootKvp.Value;
                        }
                    }
                    else if (kvp.Value is ICollection rootArray)
                    {
                        // ROOT节点是数组类型，保持原有结构
                        compatibleData[RootNodeKey] = rootArray;
                    }
                    else
                    {
                        // ROOT节点是基本类型，直接使用
                        compatibleData[RootNodeKey] = kvp.Value;
                    }
                }
                else
                {
                    // 非ROOT节点，直接复制
                    compatibleData[kvp.Key] = kvp.Value;
                }
            }

            return compatibleData;
        }

        /// <summary>
        /// 检查变量路径是否指向ROOT节点
        /// </summary>
        /// <param name="parts">变量路径</param>
        /// <returns>是否指向ROOT节点</returns>
        private static bool IsVariablePathPointingToRoot(string[] parts)
        {
            return parts.Length > 0 && parts.Last() == RootNodeKey;
        }

        /// <summary>
        /// 为变量路径访问提供ROOT节点兼容性处理
        /// </summary>
        /// <param name="result">原始结果</param>
        /// <param name="variablePath">变量路径，如 "stepId.ROOT" 或 "ROOT"</param>
        /// <returns>处理后的结果</returns>
        public static object ProcessVariablePathResult(object result, string variablePath)
        {
            if (string.IsNullOrEmpty(variablePath)) return false;

            var parts = variablePath.Split('.', StringSplitOptions.RemoveEmptyEntries);

            return ProcessVariablePathResult(result, parts);
        }

        internal static object ProcessVariablePathResult(object result, string[] parts)
        {
            // 如果变量路径指向ROOT节点
            if (IsVariablePathPointingToRoot(parts))
            {
                switch (result)
                {
                    case null:
                        return null;
                    // 情况1：字典中包含ROOT键，直接返回ROOT节点的值
                    case IDictionary<string, object> dict when dict.TryGetValue(RootNodeKey, out var pathResult):
                        return pathResult;
                    // 情况2：字典中没有ROOT键但只有一个result键，将result的值作为ROOT节点的值
                    // 这种情况通常发生在输出参数配置为ROOT节点但实际数据中只有result字段时
                    case IDictionary<string, object> { Count: 1 } dict:
                        return dict.First();
                }
            }

            return result;
        }
    }
}
