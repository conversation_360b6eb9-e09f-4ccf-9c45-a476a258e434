using GCP.Iot.Models;
using GCP.DataAccess;
using LinqToDB;
using GCP.Cache;
using EasyCaching.Core;
using GCP.Common;
using Serilog;
using Medallion.Threading;

namespace GCP.Iot.Services
{
    /// <summary>
    /// 设备初始化器，用于从数据库加载设备配置并初始化设备通信
    /// </summary>
    class EquipmentInitializer : IDisposable
    {
        private readonly DriverManager _driverManager;
        private readonly DriverMetadataService _driverMetadataService;
        private readonly EquipmentCommunicationManager _communicationManager;
        private readonly DistributedInstanceManager _instanceManager;
        private readonly IMessageBus _iotBus;
        private static string EquipmentInitTopic => "equipment.init";

        public EquipmentInitializer(
            DriverManager driverManager,
            DriverMetadataService driverMetadataService,
            EquipmentCommunicationManager communicationManager,
            IEasyCachingProvider cachingProvider,
            IMessageBusManager messageBusManager,
            IDistributedLockProvider distributedLockProvider)
        {
            _driverManager = driverManager;
            _driverMetadataService = driverMetadataService;
            _communicationManager = communicationManager;
            _instanceManager = new DistributedInstanceManager(
                "IotDriver",
                cachingProvider,
                distributedLockProvider);

            // 获取Iot消息总线
            _iotBus = messageBusManager.MessageBuses[EventBusHelper.LocalIotEventBusName]
                ?? throw new InvalidOperationException("Iot消息总线未初始化");

            // 订阅资源变更事件
            _instanceManager.OnResourceChanged += HandleDriverChangedAsync;

            // 注册设备初始化消息处理
            _iotBus.SubscribeAsync(EquipmentInitTopic, HandleEquipmentInitMessageAsync, new ConsumerOptions
            {
                Name = "EquipmentInitHandler",
                Topic = EquipmentInitTopic,
                BusName = _iotBus.Name,
            });
        }

        private async Task HandleEquipmentInitMessageAsync(MessageEnvelope message, CancellationToken cancellationToken)
        {
            var messageData = JsonHelper.Deserialize<EquipmentInitMessage>(JsonHelper.Serialize(message.Payload));
            try
            {
                // 检查消息是否应该由当前实例处理
                if (await _instanceManager.ShouldHandleResourceAsync(messageData.DriverCode))
                {
                    Log.Information("收到设备初始化消息: EquipmentId={MessageDataEquipmentId}", messageData.EquipmentId);
                    await InitializeEquipmentAsync(messageData.EquipmentId);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理设备初始化消息失败: EquipmentId={MessageDataEquipmentId}", messageData.EquipmentId);
            }
        }

        private class EquipmentInitMessage
        {
            public string EquipmentId { get; set; } = string.Empty;
            public string DriverCode { get; set; } = string.Empty;
        }

        private async Task HandleDriverChangedAsync(string driverCode, bool isAssigned)
        {
            try
            {
                if (!isAssigned)
                {
                    // 获取当前运行中的设备
                    var runningEquipments = _communicationManager.GetRunningEquipments()
                        .Where(e => e.DriverCode == driverCode)
                        .ToList();

                    if (runningEquipments.Any())
                    {
                        Log.Information("驱动 {DriverCode} 重分配，开始停止 {RunningEquipmentsCount} 个运行中的设备", driverCode, runningEquipments.Count);

                        foreach (var task in runningEquipments)
                        {
                            try
                            {
                                // 停止设备通信
                                await task.StopAsync();
                                task.Dispose();

                                Log.Debug("由于驱动 {DriverCode} 重分配，设备 {TaskEquipmentCode} 已停止", driverCode, task.EquipmentCode);
                            }
                            catch (Exception ex)
                            {
                                Log.Error(ex, "停止设备失败: {TaskEquipmentCode}", task.EquipmentCode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理驱动变更失败: DriverCode={DriverCode}, IsAssigned={IsAssigned}", driverCode, isAssigned);
            }
        }

        public async Task<bool> ShouldHandleDriverAsync(string driverCode)
        {
            return await _instanceManager.ShouldHandleResourceAsync(driverCode);
        }

        /// <summary>
        /// 初始化所有状态为活跃的设备
        /// </summary>
        public async Task InitializeAllActiveEquipmentAsync()
        {
            try
            {
                Log.Information("开始初始化所有活跃的设备");

                // 注册实例
                await _instanceManager.RegisterInstanceAsync();

                await using var db = new GcpDb();
                // 查询所有状态为活跃的设备，按驱动分组
                var activeEquipments = db.LcIotEquipment
                    .Where(e => e.State == 1 && e.Status == 1)
                    .ToList()
                    .GroupBy(e => e.DriverCode)
                    .ToDictionary(g => g.Key, g => g.ToList());

                if (activeEquipments.Count == 0)
                {
                    return;
                }

                Log.Information("找到{Sum}个活跃设备，分布在{ActiveEquipmentsCount}个驱动中", activeEquipments.Sum(g => g.Value.Count), activeEquipments.Count);

                foreach (var driverGroup in activeEquipments)
                {
                    var driverCode = driverGroup.Key;
                    var equipments = driverGroup.Value;

                    try
                    {
                        // 检查驱动是否应该由当前实例处理
                        if (await _instanceManager.ShouldHandleResourceAsync(driverCode))
                        {
                            Log.Information("驱动 {DriverCode} 分配到当前实例，包含 {EquipmentsCount} 个设备", driverCode, equipments.Count);
                            foreach (var equipment in equipments)
                            {
                                try
                                {
                                    await InitializeEquipmentFromDatabaseAsync(equipment, db);
                                }
                                catch (Exception ex)
                                {
                                    Log.Error("初始化设备失败: {EquipmentEquipmentCode} 错误信息：{ErrorMessage}", equipment.EquipmentCode, ex.Message);
                                }
                            }
                        }
                        else
                        {
                            Log.Information("驱动 {DriverCode} 由其他实例处理，包含 {EquipmentsCount} 个设备", driverCode, equipments.Count);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "处理驱动失败: {DriverCode}", driverCode);
                        // 发生错误时释放驱动
                        await _instanceManager.ReleaseResourceAsync(driverCode);
                    }
                }

                Log.Information("所有活跃设备初始化完成");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化所有设备失败");
                throw;
            }
        }

        /// <summary>
        /// 从数据库初始化单个设备
        /// </summary>
        public async Task<bool> InitializeEquipmentAsync(string equipmentId)
        {
            await using var db = new GcpDb();
            // 查询设备信息
            var equipment = db.LcIotEquipment
                .FirstOrDefault(e => e.Id == equipmentId && e.State == 1);

            if (equipment == null)
            {
                throw new InvalidOperationException($"设备不存在: {equipmentId}");
            }

            if (equipment.Status == 1)
            {
                return true;
            }

            try
            {
                // 检查驱动是否应该由当前实例处理
                if (!await _instanceManager.ShouldHandleResourceAsync(equipment.DriverCode))
                {
                    // 获取负责该驱动的实例
                    var targetInstance = await _instanceManager.GetResourceInstanceAsync(equipment.DriverCode);
                    if (targetInstance != null)
                    {
                        Log.Information("设备 {EquipmentEquipmentCode} 的驱动 {EquipmentDriverCode} 由实例 {TargetInstance} 处理，发送初始化消息", equipment.EquipmentCode, equipment.DriverCode, targetInstance);

                        // 发送初始化消息到目标实例
                        await _iotBus.PublishAsync(EquipmentInitTopic, new EquipmentInitMessage
                        {
                            EquipmentId = equipmentId,
                            DriverCode = equipment.DriverCode
                        });
                        return true;
                    }

                    Log.Warning("设备 {EquipmentEquipmentCode} 的驱动 {EquipmentDriverCode} 当前没有可用的处理实例", equipment.EquipmentCode, equipment.DriverCode);
                    return false;
                }

                return await InitializeEquipmentFromDatabaseAsync(equipment, db);
            }
            catch (Exception ex)
            {
                Log.Error("初始化设备失败: {EquipmentEquipmentCode} 错误信息：{ErrorMessage}", equipment.EquipmentCode, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 停止设备并更新数据库状态
        /// </summary>
        public async Task StopEquipmentAsync(string equipmentId)
        {
            try
            {
                await using var db = new GcpDb();
                var equipment = db.LcIotEquipment
                    .FirstOrDefault(e => e.Id == equipmentId);

                if (equipment == null)
                {
                    throw new InvalidOperationException($"设备不存在: {equipmentId}");
                }

                var driverCode = equipment.DriverCode;
                var result = await _communicationManager.RemoveEquipmentTaskAsync(equipmentId, equipment.EquipmentCode);

                if (result)
                {
                    // 获取同驱动的其他设备
                    var runningEquipments = _communicationManager.GetRunningEquipmentsByDriver(driverCode).ToList();
                    
                    // 如果没有设备使用此驱动了
                    if (runningEquipments.Count == 0)
                    {
                        // 驱动无运行中的设备，释放资源
                        await _instanceManager.ReleaseResourceAsync(equipment.DriverCode);
                        // 释放共享驱动
                        await _driverManager.ReleaseSharedDriverAsync(driverCode);
                        Log.Information("驱动已释放: {DriverCode}", driverCode);
                    }
                    // 共享驱动且没有设备使用共享实例了
                    else if (!_communicationManager.IsSharedDriverInUse(driverCode))
                    {
                        // 如果没有设备使用共享驱动了，但还有设备使用专用驱动，则只释放共享驱动实例
                        await _driverManager.ReleaseSharedDriverAsync(driverCode);
                        Log.Information("共享驱动实例已释放: {DriverCode}", driverCode);
                    }
                }

                if (equipment.Status == 1)
                {
                    // 更新设备状态
                    equipment.Status = 0; // 0表示停止
                    await db.UpdateAsync(equipment);

                    Log.Information("设备已停止: {EquipmentEquipmentCode}", equipment.EquipmentCode);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "停止设备失败: {EquipmentId}", equipmentId);
                throw;
            }
        }

        /// <summary>
        /// 重启设备
        /// </summary>
        /// <param name="equipmentId"> 设备Id </param>
        /// <returns></returns>
        public async Task RestartEquipmentAsync(string equipmentId)
        {
            Log.Information("开始重启设备: {EquipmentId}", equipmentId);

            try
            {
                // 先停止设备
                await StopEquipmentAsync(equipmentId);

                // 清理可能存在的消息消费者
                //await CleanupDeviceMessageConsumersAsync(equipmentId);

                // 等待一小段时间确保清理完成
                await Task.Delay(500); // 增加等待时间确保清理完成

                // 重新初始化设备
                await InitializeEquipmentAsync(equipmentId);

                // 重新创建消息消费者
                //await RecreateDeviceMessageConsumersAsync(equipmentId);

                Log.Information("设备重启完成: {EquipmentId}", equipmentId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "设备重启失败: {EquipmentId}", equipmentId);
                throw;
            }
        }

        /// <summary>
        /// 从数据库初始化设备的内部实现
        /// </summary>
        private async Task<bool> InitializeEquipmentFromDatabaseAsync(LcIotEquipment equipment, GcpDb db)
        {
            var equipmentId = equipment.Id;

            // 查询设备变量
            var variables = db.LcIotEquipmentVariables
                .Where(v => v.EquipmentId == equipmentId && v.State == 1 && v.IsUpload == 1)
                .ToList();

            if (variables.Count == 0)
            {
                throw new InvalidOperationException($"设备{equipment.EquipmentCode}没有配置变量");
            }

            // 查询设备驱动配置
            var driverCode = equipment.DriverCode;
            var driverConfigs = db.LcIotDrivers
                .Where(d => d.SolutionId == equipment.SolutionId && d.ProjectId == equipment.ProjectId && d.DriverCode == driverCode && (d.EquipmentId == null || d.EquipmentId == equipmentId))
                .ToList();

            // 判断是否存在设备级别配置
            bool hasEquipmentSpecificConfig = driverConfigs.Any(d => d.EquipmentId == equipmentId);
            string driverStatus = hasEquipmentSpecificConfig ? "设备专用" : "共享";
            
            Log.Information("初始化设备: {EquipmentEquipmentCode}, 驱动: {DriverCode}({DriverStatus}), 上传变量数量: {VariablesCount}", equipment.EquipmentCode, driverCode, driverStatus, variables.Count);

            // 将数据库中的变量转换为驱动需要的格式
            var equipmentVariables = variables.Select(v => new EquipmentVariable
            {
                Id = v.Id,
                VarName = v.VarName,
                Address = v.Address,
                DataType = v.DataType,
                ArchivePeriod = v.ArchivePeriod,
                ChangeThreshold = v.ChangeThreshold
            }).ToList();

            // 设置驱动全局配置
            var globalConfigs = driverConfigs
                .Where(d => string.IsNullOrEmpty(d.EquipmentId))
                .ToDictionary(d => d.ParamKey, d => (object)d.ParamValue);

            if (globalConfigs.Count > 0)
            {
                _driverManager.SetDriverConfig(driverCode, globalConfigs);
            }

            // 设置设备特定配置
            var equipmentConfigs = driverConfigs
                .Where(d => d.EquipmentId == equipmentId)
                .ToDictionary(d => d.ParamKey, d => (object)d.ParamValue);

            if (equipmentConfigs.Count > 0)
            {
                equipmentConfigs["EquipmentId"] = equipmentId;
                _driverManager.SetEquipmentDriverConfig(driverCode, equipmentId, equipmentConfigs);
                Log.Debug("设备 {EquipmentEquipmentCode} 设置了专用驱动配置", equipment.EquipmentCode);
            }

            // 创建驱动实例并配置
            var driver = _driverManager.CreateDriver(driverCode, equipmentId);
            if (driver == null)
            {
                throw new InvalidOperationException($"创建驱动失败: {driverCode}");
            }

            // 启动设备通信
            await _communicationManager.AddEquipmentTaskAsync(
                equipmentId: equipmentId,
                equipmentCode: equipment.EquipmentCode,
                equipmentType: equipment.EquipmentType,
                driver: driver,
                variables: equipmentVariables
            );

            // 更新设备状态
            equipment.Status = 1; // 1表示运行中
            db.Update(equipment);

            Log.Information("设备 {EquipmentEquipmentCode} 初始化完成", equipment.EquipmentCode);

            return true;
        }

        /// <summary>
        /// 清理设备相关的消息消费者
        /// </summary>
        private async Task CleanupDeviceMessageConsumersAsync(string equipmentId)
        {
            try
            {
                await using var db = new GcpDb();

                // 查找与该设备相关的所有消息事件
                var deviceEvents = await db.LcMessageEvents
                    .Where(e => e.SourceType == 1) // 设备类型事件
                    .ToListAsync();

                var deviceEventMappings = await db.LcMessageEventMappings
                    .Where(m => deviceEvents.Select(e => e.Id).Contains(m.EventId) && m.SourceId == equipmentId)
                    .ToListAsync();

                // 获取消息总线管理器
                var messageBusManager = ServiceLocator.Current.GetService(typeof(IMessageBusManager)) as IMessageBusManager;

                foreach (var mapping in deviceEventMappings)
                {
                    var evt = deviceEvents.FirstOrDefault(e => e.Id == mapping.EventId);
                    if (evt != null)
                    {
                        // 移除相关的消费者
                        if (messageBusManager.Consumers.ContainsKey(evt.Id))
                        {
                            await messageBusManager.RemoveConsumerAsync(evt.Id);
                            Log.Information("已清理设备消息消费者: {EventId} for 设备: {EquipmentId}", evt.Id, equipmentId);
                        }

                        // 清理可能残留的 ResiliencePipeline
                        var pipelineKey = $"$Consumer_{evt.FunctionId}";
                        if (!string.IsNullOrWhiteSpace(pipelineKey))
                        {
                            ResiliencePipelineManager.TryRemove(pipelineKey);
                            Log.Debug("已清理 ResiliencePipeline: {PipelineKey}", pipelineKey);
                        }
                    }
                }

                Log.Information("设备消息消费者清理完成: {EquipmentId}", equipmentId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "清理设备消息消费者失败: {EquipmentId}", equipmentId);
                // 不抛出异常，允许重启流程继续
            }
        }

        /// <summary>
        /// 重新创建设备相关的消息消费者
        /// </summary>
        private async Task RecreateDeviceMessageConsumersAsync(string equipmentId)
        {
            try
            {
                await using var db = new GcpDb();

                // 查找与该设备相关的所有启用的消息事件
                var deviceEvents = await db.LcMessageEvents
                    .Where(e => e.SourceType == 1 && e.IsEnabled == 1) // 设备类型且启用的事件
                    .ToListAsync();

                var deviceEventMappings = await db.LcMessageEventMappings
                    .Where(m => deviceEvents.Select(e => e.Id).Contains(m.EventId) && m.SourceId == equipmentId)
                    .ToListAsync();

                // 获取IoT事件集成服务
                var iotEventIntegration = ServiceLocator.Current.GetService(typeof(IotEventIntegrationService)) as IotEventIntegrationService;

                foreach (var mapping in deviceEventMappings)
                {
                    var evt = deviceEvents.FirstOrDefault(e => e.Id == mapping.EventId);
                    if (evt != null)
                    {
                        try
                        {
                            // 使用强制重新创建方法
                            var messageBusManager = ServiceLocator.Current.GetService(typeof(IMessageBusManager)) as IMessageBusManager;
                            var consumerOptions = new ConsumerOptions
                            {
                                Name = evt.Id,
                                Topic = evt.EventName,
                                ConsumerId = evt.FunctionId,
                                BusName = EventBusHelper.LocalIotEventBusName,
                                IsEnabled = evt.IsEnabled == 1,
                                IsFlow = true,
                                Settings = new Dictionary<string, string>
                                {
                                    { "SolutionId", evt.SolutionId },
                                    { "ProjectId", evt.ProjectId },
                                    { "DriverCode", mapping.SourceId }, // 这里应该是设备的驱动代码
                                }
                            };

                            await messageBusManager.RecreateConsumerAsync(consumerOptions);
                            Log.Information("已强制重新创建设备消息消费者: {EventId} for 设备: {EquipmentId}", evt.Id, equipmentId);
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "重新创建设备消息消费者失败: {EventId} for 设备: {EquipmentId}", evt.Id, equipmentId);
                        }
                    }
                }

                Log.Information("设备消息消费者重新创建完成: {EquipmentId}", equipmentId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "重新创建设备消息消费者失败: {EquipmentId}", equipmentId);
                // 不抛出异常，允许重启流程继续
            }
        }

        public void Dispose()
        {
            // 取消订阅资源变更事件
            if (_instanceManager != null)
            {
                _instanceManager.OnResourceChanged -= HandleDriverChangedAsync;
                (_instanceManager as IDisposable)?.Dispose();
            }

            // 取消订阅消息处理
            _iotBus?.UnsubscribeAsync(EquipmentInitTopic);
        }
    }
}