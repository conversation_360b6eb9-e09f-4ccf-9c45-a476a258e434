import{d as O,h as a,ab as d,ac as y,ad as m}from"./index-CIth6Xtx.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 25 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M3.67105 1.59007L3.66692 1.58594 2.25269 3.00013 4.39573 5.14323C3.57972 5.59808 2.79318 6.12533 2.0455 6.72498L1.26636 7.34987 12.6671 21.6008 16.3052 17.053 21.6665 22.4144 23.0764 21.0045 3.66675 1.59437 3.67105 1.59007zM18.8156 13.9151L24.0678 7.34974 23.2887 6.72485C19.0212 3.30228 13.4881 2.23837 8.43366 3.53293L18.8156 13.9151z"}}]},w=O({name:"WifiOff1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=d(t),f=a(()=>["t-icon","t-icon-wifi-off-1-filled",i.value]),p=a(()=>s(s({},c.value),r.style)),u=a(()=>({class:f.value,style:p.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(b,u.value)}});export{w as default};
