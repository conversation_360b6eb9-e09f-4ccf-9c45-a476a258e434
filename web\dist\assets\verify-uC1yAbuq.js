import{d as y,h as a,ab as C,ac as O,ad as d}from"./index-CIth6Xtx.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1 3L23 3V21H1L1 3ZM3 5L3 19H21V5L3 5ZM15.5 9.5C16.0523 9.5 16.5 9.94772 16.5 10.5C16.5 11.0523 16.0523 11.5 15.5 11.5C14.9477 11.5 14.5 11.0523 14.5 10.5C14.5 9.94772 14.9477 9.5 15.5 9.5ZM17.9003 12.2999C18.2769 11.7985 18.5 11.1753 18.5 10.5C18.5 8.84315 17.1569 7.5 15.5 7.5C13.8431 7.5 12.5 8.84315 12.5 10.5C12.5 11.1753 12.7231 11.7985 13.0997 12.2999C12.1283 13.0297 11.5 14.1915 11.5 15.5V16.5H13.5V15.5C13.5 14.3954 14.3954 13.5 15.5 13.5C16.6046 13.5 17.5 14.3954 17.5 15.5V16.5H19.5V15.5C19.5 14.1915 18.8717 13.0297 17.9003 12.2999ZM5 9H9.5V11H5V9ZM5 13H9.5V15H5V13Z"}}]},g=y({name:"VerifyIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=C(t),p=a(()=>["t-icon","t-icon-verify",o.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(m,f.value)}});export{g as default};
