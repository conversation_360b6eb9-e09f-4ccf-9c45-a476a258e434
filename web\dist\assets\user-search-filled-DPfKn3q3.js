import{d,h as a,ab as O,ac as y,ad as C}from"./index-CIth6Xtx.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.5 2C8.46243 2 6 4.46243 6 7.5 6 10.5376 8.46243 13 11.5 13 14.5376 13 17 10.5376 17 7.5 17 4.46243 14.5376 2 11.5 2zM13 17.75C13 15.1266 15.1266 13 17.75 13 20.3734 13 22.5 15.1266 22.5 17.75 22.5 18.7002 22.221 19.5852 21.7405 20.3276L23.4142 21.999 22.001 23.4142 20.326 21.7415C19.5839 22.2214 18.6995 22.5 17.75 22.5 15.1266 22.5 13 20.3734 13 17.75zM17.75 15C16.2312 15 15 16.2312 15 17.75 15 19.2688 16.2312 20.5 17.75 20.5 19.2688 20.5 20.5 19.2688 20.5 17.75 20.5 16.2312 19.2688 15 17.75 15z"}},{tag:"path",attrs:{fill:"currentColor",d:"M12.7495 14C11.9649 15.0446 11.5 16.343 11.5 17.75C11.5 19.3913 12.1326 20.8848 13.1674 22H2V20C2 16.6863 4.68629 14 8 14H12.7495Z"}}]},m=d({name:"UserSearchFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-user-search-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(h,f.value)}});export{m as default};
