import{d as v,h as a,ab as d,ac as O,ad as y}from"./index-CIth6Xtx.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 9C6.92138 9 7.89295 9.23169 8.67602 9.75374C9.46716 10.2812 10 11.0748 10 12C10 12.9252 9.46716 13.7188 8.67602 14.2463C7.89295 14.7683 6.92138 15 6 15C5.07862 15 4.10705 14.7683 3.32398 14.2463C2.53284 13.7188 2 12.9252 2 12C2 11.0748 2.53284 10.2812 3.32398 9.75374C4.10705 9.23169 5.07862 9 6 9ZM18 9C18.9214 9 19.893 9.23169 20.676 9.75374C21.4672 10.2812 22 11.0748 22 12C22 12.9252 21.4672 13.7188 20.676 14.2463C19.893 14.7683 18.9214 15 18 15C17.0786 15 16.107 14.7683 15.324 14.2463C14.5328 13.7188 14 12.9252 14 12C14 11.0748 14.5328 10.2812 15.324 9.75374C16.107 9.23169 17.0786 9 18 9ZM10.998 10.9961H13.002V13H10.998V10.9961Z"}}]},g=v({name:"UsercaseLinkFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-usercase-link-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>O(m,C.value)}});export{g as default};
